<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-P6DC2C9Q7T"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-P6DC2C9Q7T');
    </script>
    <title>How To Use - Minecraft Location Tracker</title>
    <meta name="description"
        content="Learn how to use the Minecraft Location Tracker to save, organize, and share coordinates across multiple Minecraft worlds. Includes tutorials on world management, location tracking, and using tools.">
    <meta name="keywords"
        content="Minecraft, coordinates, location tracker, tutorial, how to use, Nether portal calculator">
    <meta name="author" content="Minecraft Location Tracker">
    <meta name="robots" content="index, follow">

    <link rel="canonical" href="https://minecraft-location-tracker.com/how-to-use.html">
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=VT323&display=swap" rel="stylesheet">
    <style>
        .how-to-section {
            margin-bottom: 30px;
        }

        .how-to-section h3 {
            color: var(--dirt-brown);
            margin-bottom: 10px;
            border-bottom: 2px solid var(--grass-green);
            padding-bottom: 5px;
        }

        .how-to-step {
            margin-bottom: 15px;
            padding-left: 20px;
            position: relative;
        }

        .how-to-step:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: var(--grass-green);
        }

        .note-box {
            background-color: #FFF9C4;
            border: 2px solid #FBC02D;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .note-box h4 {
            color: #F57F17;
            margin-bottom: 10px;
        }

        .screenshot {
            max-width: 100%;
            border: 3px solid var(--dirt-brown);
            margin: 10px 0;
            border-radius: 5px;
        }

        .nav-btn {
            display: inline-block;
            margin-top: 20px;
            margin-right: 10px;
            background-color: var(--dirt-brown);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border: 2px solid #5D4037;
            transition: all 0.3s;
        }

        .nav-btn:hover {
            background-color: #5D4037;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>How To Use Minecraft Location Tracker</h1>
            <p class="tagline">A comprehensive guide to using our coordinate tracking tool</p>
            <div class="creator-info">
                <h3>Created by Shushie</h3>
                <div class="creator-links">
                    <a href="https://www.youtube.com/@Shushie_valorant?sub_confirmation=1" class="creator-link" target="_blank" rel="noopener">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23FF0000' d='M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z'/%3E%3C/svg%3E" alt="YouTube">
                        Subscribe on YouTube
                    </a>
                    <a href="https://razorpay.me/@shushie" class="creator-link" target="_blank" rel="noopener">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23795548' d='M20 3H4v10c0 2.21 1.79 4 4 4h6c2.21 0 4-1.79 4-4v-3h2c1.11 0 2-.89 2-2V5c0-1.11-.89-2-2-2zm0 5h-2V5h2v3zM4 19h16v2H4z'/%3E%3C/svg%3E" alt="Coffee">
                        Buy Me a Coffee ☕
                    </a>
                </div>
            </div>
            <nav>
                <a href="index.html" class="nav-btn">Back to Home</a>
            </nav>
        </header>

        <main>
            <section>
                <h2>How To Use This Website</h2>

                <div class="how-to-section">
                    <h3>Managing Worlds</h3>
                    <div class="how-to-step">
                        <p><strong>Creating a New World:</strong> Click on the "Create New World" button at the top of
                            the page. Enter a name for your Minecraft world and click "Create".</p>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Switching Between Worlds:</strong> Use the world selector dropdown to switch between
                            your different Minecraft worlds. All locations specific to that world will be displayed.</p>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Deleting a World:</strong> Select the world you want to delete, then click the
                            "Delete World" button. You'll be asked to confirm this action.</p>
                    </div>
                </div>

                <div class="how-to-section">
                    <h3>Adding Locations</h3>
                    <div class="how-to-step">
                        <p><strong>Step 1:</strong> First, select the world where you want to add the location.</p>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Step 2:</strong> Fill out the "Add New Location" form with the following details:</p>
                        <ul>
                            <li><strong>Location Name:</strong> A descriptive name (e.g., "Main Base", "Desert Temple").
                            </li>
                            <li><strong>Coordinates:</strong> The X, Y, and Z coordinates from your Minecraft game
                                (press F3 in-game to see these).</li>
                            <li><strong>Dimension:</strong> Select Overworld, Nether, or The End.</li>
                            <li><strong>Category:</strong> Choose a category that best fits your location.</li>
                            <li><strong>Description:</strong> Add any helpful notes or details about this location.</li>
                            <li><strong>Color Tag:</strong> Optionally pick a color to visually identify this location.
                            </li>
                        </ul>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Step 3:</strong> Click "Save Location" to add it to your world.</p>
                    </div>
                </div>

                <div class="how-to-section">
                    <h3>Managing Locations</h3>
                    <div class="how-to-step">
                        <p><strong>Viewing Locations:</strong> All locations for the currently selected world will
                            appear in the "Saved Locations" section.</p>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Filtering Locations:</strong> Use the filter dropdowns to show only specific
                            dimensions or categories. You can also use the search box to find locations by name or
                            description.</p>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Editing a Location:</strong> Click the "Edit" button on any location card. The form
                            will be pre-filled with that location's details. Make your changes and click "Update
                            Location".</p>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Copying Coordinates:</strong> Click the "Copy Coords" button to copy the location's
                            coordinates to your clipboard. This makes it easy to share with friends or paste into
                            Minecraft commands.</p>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Deleting a Location:</strong> Click the "Delete" button on any location card. You'll
                            be asked to confirm before it's removed.</p>
                    </div>
                </div>

                <div class="how-to-section">
                    <h3>Using the Tools</h3>
                    <div class="how-to-step">
                        <p><strong>Nether Portal Calculator:</strong> This tool helps you find corresponding coordinates
                            between the Overworld and Nether (8:1 ratio).</p>
                        <ol>
                            <li><strong>Overworld to Nether:</strong> Enter your Overworld X and Z coordinates, leave
                                the Nether fields empty, then click "Calculate". The tool will show where to build your
                                portal in the Nether.</li>
                            <li><strong>Nether to Overworld:</strong> Enter your Nether X and Z coordinates, leave the
                                Overworld fields empty, then click "Calculate". The tool will show where your portal
                                will appear in the Overworld.</li>
                            <li>The tool will automatically fill in the corresponding coordinates after calculation.
                            </li>
                        </ol>
                        <p><em>Note: In Minecraft, the Nether is compressed 8:1 compared to the Overworld. This means 1
                                block in the Nether equals 8 blocks in the Overworld.</em></p>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Distance Calculator:</strong> Calculate the distance between any two saved locations.
                        </p>
                        <ol>
                            <li>Select two locations from the dropdowns</li>
                            <li>Click "Calculate Distance"</li>
                            <li>View both the 3D distance (including Y axis) and horizontal distance (X and Z only)</li>
                        </ol>
                    </div>
                </div>

                <div class="how-to-section">
                    <h3>Data Management</h3>
                    <div class="how-to-step">
                        <p><strong>Exporting Data:</strong> Click the "Export Data" button to download a JSON file
                            containing all your worlds and locations.</p>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Importing Data:</strong> Click "Import Data" to upload a previously exported file.
                            This will add those worlds and locations to your current data.</p>
                    </div>
                    <div class="how-to-step">
                        <p><strong>Clearing All Data:</strong> The "Clear All Data" button will remove all worlds and
                            locations. Use with caution!</p>
                    </div>
                </div>

                <div class="note-box">
                    <h4>Important Note About Data Storage</h4>
                    <p>All your worlds and locations are stored in your browser's local storage. This means:</p>
                    <ul>
                        <li>If you clear your browser data, all your saved locations will be lost</li>
                        <li>Your data is specific to this browser on this device</li>
                        <li>To back up your data or transfer it to another device, use the Export/Import feature</li>
                        <li>You can share your locations with friends by exporting your data and sending them the file
                        </li>
                    </ul>
                    <p><strong>Recommendation:</strong> Regularly export your data as a backup, especially before
                        clearing browser data or switching devices.</p>
                </div>
            </section>
        </main>

        <footer>
            <p>Minecraft Location Tracker &copy; 2025 - An open-source tool for Minecraft players</p>
            <p class="seo-description">This free web tool helps Minecraft players track coordinates of important
                locations like bases, villages, portals, and more across multiple worlds. Save and organize your
                Minecraft coordinates easily!</p>
        </footer>
    </div>
</body>

</html>
