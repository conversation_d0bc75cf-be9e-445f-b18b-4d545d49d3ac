document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const locationForm = document.getElementById('location-form');
    const locationsContainer = document.getElementById('locations-container');
    const filterDimension = document.getElementById('filter-dimension');
    const filterCategory = document.getElementById('filter-category');
    const searchInput = document.getElementById('search');
    const exportDataBtn = document.getElementById('export-data');
    const importDataInput = document.getElementById('import-data');
    const clearDataBtn = document.getElementById('clear-data');
    const calculateNetherBtn = document.getElementById('calculate-nether');
    const calculateDistanceBtn = document.getElementById('calculate-distance');
    const location1Select = document.getElementById('location1');
    const location2Select = document.getElementById('location2');
    
    // World Management Elements
    const worldSelector = document.getElementById('world-selector');
    const createWorldBtn = document.getElementById('create-world-btn');
    const deleteWorldBtn = document.getElementById('delete-world-btn');
    const createWorldModal = document.getElementById('create-world-modal');
    const createWorldForm = document.getElementById('create-world-form');
    const closeModalBtn = document.querySelector('.close-modal');
    
    // Initialize worlds from localStorage or create an empty array
    let worlds = JSON.parse(localStorage.getItem('minecraft-worlds')) || [];
    let currentWorldId = localStorage.getItem('minecraft-current-world') || (worlds.length > 0 ? worlds[0].id : null);
    
    // Get current world object
    function getCurrentWorld() {
        return worlds.find(world => world.id === currentWorldId) || null;
    }
    
    // Get locations for current world
    function getCurrentLocations() {
        const world = getCurrentWorld();
        return world ? world.locations : [];
    }
    
    // Save worlds to localStorage
    function saveWorlds() {
        localStorage.setItem('minecraft-worlds', JSON.stringify(worlds));
        localStorage.setItem('minecraft-current-world', currentWorldId);
        updateWorldSelector();
        updateLocationSelects();
    }
    
    // Generate a unique ID
    function generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    
    // Update world selector dropdown
    function updateWorldSelector() {
        // Clear existing options except the first one
        while (worldSelector.options.length > 1) {
            worldSelector.remove(1);
        }
        
        // Add worlds as options
        worlds.forEach(world => {
            const option = document.createElement('option');
            option.value = world.id;
            option.textContent = world.name;
            
            if (world.id === currentWorldId) {
                option.selected = true;
            }
            
            worldSelector.appendChild(option);
        });
        
        // Enable/disable delete button
        deleteWorldBtn.disabled = worlds.length <= 1;
    }
    
    // Show create world modal
    function showCreateWorldModal() {
        createWorldModal.style.display = 'block';
    }
    
    // Hide create world modal
    function hideCreateWorldModal() {
        createWorldModal.style.display = 'none';
        createWorldForm.reset();
    }
    
    // Create a new world
    function createWorld(event) {
        event.preventDefault();
        
        const worldName = document.getElementById('world-name').value.trim();
        
        if (!worldName) {
            alert('Please enter a world name');
            return;
        }
        
        const newWorld = {
            id: generateId(),
            name: worldName,
            locations: []
        };
        
        worlds.push(newWorld);
        currentWorldId = newWorld.id;
        
        saveWorlds();
        renderLocations();
        hideCreateWorldModal();
    }
    
    // Delete current world
    function deleteWorld() {
        if (worlds.length <= 1) {
            alert('You cannot delete the only world. Create another world first.');
            return;
        }
        
        const currentWorld = getCurrentWorld();
        
        if (!currentWorld) {
            return;
        }
        
        if (confirm(`Are you sure you want to delete the world "${currentWorld.name}" and all its locations? This cannot be undone!`)) {
            worlds = worlds.filter(world => world.id !== currentWorldId);
            currentWorldId = worlds.length > 0 ? worlds[0].id : null;
            
            saveWorlds();
            renderLocations();
        }
    }
    
    // Change current world
    function changeWorld() {
        const selectedWorldId = worldSelector.value;
        
        if (selectedWorldId) {
            currentWorldId = selectedWorldId;
            saveWorlds();
            renderLocations();
        }
    }
    
    // Render all locations for the current world
    function renderLocations() {
        locationsContainer.innerHTML = '';
        
        // Check if there are any worlds
        if (worlds.length === 0) {
            locationsContainer.innerHTML = '<div class="empty-state"><p>Welcome to Minecraft Location Tracker!</p><p>Start by creating a new world using the "Create New World" button above.</p></div>';
            return;
        }
        
        // Check if a world is selected
        if (!currentWorldId || !getCurrentWorld()) {
            locationsContainer.innerHTML = '<div class="empty-state"><p>No world selected. Please select or create a world.</p></div>';
            return;
        }
        
        const locations = getCurrentLocations();
        
        // Check if the current world has any locations
        if (locations.length === 0) {
            locationsContainer.innerHTML = '<div class="empty-state"><p>This world has no saved locations yet.</p><p>Add your first location using the form above.</p></div>';
            return;
        }
        
        const filteredLocations = locations.filter(location => {
            const dimensionMatch = filterDimension.value === 'all' || location.dimension === filterDimension.value;
            const categoryMatch = filterCategory.value === 'all' || location.category === filterCategory.value;
            const searchMatch = location.name.toLowerCase().includes(searchInput.value.toLowerCase()) || 
                               location.description.toLowerCase().includes(searchInput.value.toLowerCase());
            
            return dimensionMatch && categoryMatch && searchMatch;
        });
        
        if (filteredLocations.length === 0) {
            locationsContainer.innerHTML = '<p class="no-results">No locations found. Try adjusting your filters or add a new location.</p>';
            return;
        }
        
        filteredLocations.forEach(location => {
            const template = document.getElementById('location-card-template');
            const locationCard = document.importNode(template.content, true).querySelector('.location-card');
            
            // Add dimension and category classes
            locationCard.classList.add(`dimension-${location.dimension}`);
            
            // Set location data
            locationCard.querySelector('.location-name').textContent = location.name;
            
            const categoryElement = locationCard.querySelector('.location-category');
            categoryElement.textContent = location.category.charAt(0).toUpperCase() + location.category.slice(1);
            categoryElement.classList.add(`category-${location.category}`);
            
            locationCard.querySelector('.coord-x').textContent = `X: ${location.x}`;
            locationCard.querySelector('.coord-y').textContent = `Y: ${location.y}`;
            locationCard.querySelector('.coord-z').textContent = `Z: ${location.z}`;
            
            const dimensionText = location.dimension.charAt(0).toUpperCase() + location.dimension.slice(1);
            locationCard.querySelector('.location-dimension').textContent = dimensionText;
            
            locationCard.querySelector('.location-description').textContent = location.description;
            
            // Set custom color
            locationCard.style.borderTop = `5px solid ${location.color}`;
            
            // Add event listeners
            locationCard.querySelector('.btn-copy').addEventListener('click', () => {
                const coordsText = `${location.x}, ${location.y}, ${location.z}`;
                navigator.clipboard.writeText(coordsText).then(() => {
                    alert(`Coordinates copied: ${coordsText}`);
                });
            });
            
            locationCard.querySelector('.btn-edit').addEventListener('click', () => {
                editLocation(location);
            });
            
            locationCard.querySelector('.btn-delete').addEventListener('click', () => {
                if (confirm(`Are you sure you want to delete "${location.name}"?`)) {
                    const world = getCurrentWorld();
                    world.locations = world.locations.filter(loc => loc.id !== location.id);
                    saveWorlds();
                    renderLocations();
                }
            });
            
            // Add data attribute for identification
            locationCard.dataset.id = location.id;
            
            locationsContainer.appendChild(locationCard);
        });
    }
    
    // Edit a location
    function editLocation(location) {
        // Fill form with location data
        document.getElementById('location-name').value = location.name;
        document.getElementById('coord-x').value = location.x;
        document.getElementById('coord-y').value = location.y;
        document.getElementById('coord-z').value = location.z;
        document.getElementById('dimension').value = location.dimension;
        document.getElementById('category').value = location.category;
        document.getElementById('description').value = location.description;
        document.getElementById('color').value = location.color;
        
        // Store the original location ID to track which location we're editing
        locationForm.dataset.editingLocationId = location.id;
        
        // Change button text
        document.querySelector('#location-form button[type="submit"]').textContent = 'Update Location';
        
        // Scroll to form
        document.querySelector('.add-location').scrollIntoView({ behavior: 'smooth' });
    }
    
    // Add or update a location
    function addLocation(event) {
        event.preventDefault();
        
        if (!currentWorldId) {
            alert('Please select or create a world first.');
            return;
        }
        
        const world = getCurrentWorld();
        
        if (!world) {
            return;
        }
        
        // Check if we're editing an existing location
        const editingLocationId = locationForm.dataset.editingLocationId;
        
        // Gather form data
        const formData = {
            name: document.getElementById('location-name').value,
            x: parseInt(document.getElementById('coord-x').value),
            y: parseInt(document.getElementById('coord-y').value),
            z: parseInt(document.getElementById('coord-z').value),
            dimension: document.getElementById('dimension').value,
            category: document.getElementById('category').value,
            description: document.getElementById('description').value,
            color: document.getElementById('color').value
        };
        
        // Validate form data
        if (!formData.name || isNaN(formData.x) || isNaN(formData.y) || isNaN(formData.z)) {
            alert('Please fill in all required fields with valid values.');
            return;
        }
        
        if (editingLocationId) {
            // We're updating an existing location
            const locationIndex = world.locations.findIndex(loc => loc.id === editingLocationId);
            
            if (locationIndex !== -1) {
                // Update the existing location
                world.locations[locationIndex] = {
                    ...world.locations[locationIndex],
                    ...formData
                };
            }
            
            // Reset the editing state
            locationForm.dataset.editingLocationId = '';
            document.querySelector('#location-form button[type="submit"]').textContent = 'Save Location';
        } else {
            // We're adding a new location
            const newLocation = {
                id: generateId(),
                ...formData
            };
            
            world.locations.push(newLocation);
        }
        
        saveWorlds();
        renderLocations();
        
        // Reset form
        locationForm.reset();
    }
    
    // Export data (all worlds)
    function exportData() {
        const dataStr = JSON.stringify(worlds, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
        
        const exportFileDefaultName = 'minecraft-worlds.json';
        
        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    }
    
    // Import data
    function importData(event) {
        const file = event.target.files[0];
        
        if (file) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);
                    
                    if (Array.isArray(importedData)) {
                        if (confirm('This will merge the imported worlds with your current worlds. Continue?')) {
                            // Check for world ID conflicts and resolve them
                            const existingWorldIds = worlds.map(world => world.id);
                            
                            importedData.forEach(importedWorld => {
                                if (existingWorldIds.includes(importedWorld.id)) {
                                    // Generate a new ID for the imported world
                                    importedWorld.id = generateId();
                                }
                                
                                worlds.push(importedWorld);
                            });
                            
                            saveWorlds();
                            renderLocations();
                            alert('Worlds imported successfully!');
                        }
                    } else {
                        alert('Invalid data format. Please import a valid JSON file.');
                    }
                } catch (error) {
                    alert('Error importing data: ' + error.message);
                }
            };
            
            reader.readAsText(file);
        }
    }
    
    // Clear all data
    function clearData() {
        if (confirm('Are you sure you want to delete ALL worlds and locations? This cannot be undone!')) {
            worlds = [];
            currentWorldId = null;
            saveWorlds();
            renderLocations();
        }
    }
    
    // Calculate Nether coordinates
    function calculateNetherCoords() {
        const overworldX = parseFloat(document.getElementById('overworld-x').value);
        const overworldZ = parseFloat(document.getElementById('overworld-z').value);
        const netherX = parseFloat(document.getElementById('nether-x').value);
        const netherZ = parseFloat(document.getElementById('nether-z').value);
        
        const resultElement = document.getElementById('nether-result');
        
        // Check which direction to calculate (Overworld to Nether or Nether to Overworld)
        if (!isNaN(overworldX) && !isNaN(overworldZ) && 
            (isNaN(netherX) || isNaN(netherZ) || 
             document.getElementById('nether-x').value === '' || 
             document.getElementById('nether-z').value === '')) {
            // Calculate Overworld to Nether
            const calculatedNetherX = Math.floor(overworldX / 8);
            const calculatedNetherZ = Math.floor(overworldZ / 8);
            
            resultElement.innerHTML = `
                <strong>Nether coordinates:</strong><br>
                X: ${calculatedNetherX}, Z: ${calculatedNetherZ}<br>
                <em>Build your portal at these coordinates in the Nether.</em>
            `;
            
            // Fill in the calculated values
            document.getElementById('nether-x').value = calculatedNetherX;
            document.getElementById('nether-z').value = calculatedNetherZ;
        } 
        else if (!isNaN(netherX) && !isNaN(netherZ) && 
                 (isNaN(overworldX) || isNaN(overworldZ) || 
                  document.getElementById('overworld-x').value === '' || 
                  document.getElementById('overworld-z').value === '')) {
            // Calculate Nether to Overworld
            const calculatedOverworldX = Math.floor(netherX * 8);
            const calculatedOverworldZ = Math.floor(netherZ * 8);
            
            resultElement.innerHTML = `
                <strong>Overworld coordinates:</strong><br>
                X: ${calculatedOverworldX}, Z: ${calculatedOverworldZ}<br>
                <em>Build your portal at these coordinates in the Overworld.</em>
            `;
            
            // Fill in the calculated values
            document.getElementById('overworld-x').value = calculatedOverworldX;
            document.getElementById('overworld-z').value = calculatedOverworldZ;
        }
        else if (!isNaN(overworldX) && !isNaN(overworldZ) && !isNaN(netherX) && !isNaN(netherZ)) {
            // Both sets of coordinates are filled in
            resultElement.innerHTML = `
                <strong>Both coordinate sets are filled.</strong><br>
                Clear one set of coordinates to calculate in that direction.
            `;
        }
        else {
            // No valid coordinates entered
            resultElement.textContent = 'Please enter valid coordinates in either Overworld or Nether fields.';
        }
    }
    
    // Calculate distance between locations
    function calculateDistance() {
        const location1Id = location1Select.value;
        const location2Id = location2Select.value;
        
        if (!location1Id || !location2Id) {
            document.getElementById('distance-result').textContent = 'Please select two locations.';
            return;
        }
        
        // Find locations across all worlds
        let loc1, loc2;
        
        for (const world of worlds) {
            if (!loc1) {
                loc1 = world.locations.find(loc => loc.id === location1Id);
            }
            
            if (!loc2) {
                loc2 = world.locations.find(loc => loc.id === location2Id);
            }
            
            if (loc1 && loc2) {
                break;
            }
        }
        
        if (!loc1 || !loc2) {
            document.getElementById('distance-result').textContent = 'One or both locations not found.';
            return;
        }
        
        if (loc1.dimension !== loc2.dimension) {
            document.getElementById('distance-result').textContent = 'Cannot calculate distance between different dimensions.';
            return;
        }
        
        const dx = loc2.x - loc1.x;
        const dy = loc2.y - loc1.y;
        const dz = loc2.z - loc1.z;
        
        const distance = Math.sqrt(dx*dx + dy*dy + dz*dz).toFixed(2);
        const horizontalDistance = Math.sqrt(dx*dx + dz*dz).toFixed(2);
        
        document.getElementById('distance-result').innerHTML = `
            <strong>3D Distance:</strong> ${distance} blocks<br>
            <strong>Horizontal Distance:</strong> ${horizontalDistance} blocks<br>
            <em>From "${loc1.name}" to "${loc2.name}"</em>
        `;
    }
    
    // Update location select dropdowns
    function updateLocationSelects() {
        // Clear existing options except the first one
        while (location1Select.options.length > 1) {
            location1Select.remove(1);
        }
        
        while (location2Select.options.length > 1) {
            location2Select.remove(1);
        }
        
        // Add locations from all worlds as options
        worlds.forEach(world => {
            // Create an optgroup for each world
            const optgroup1 = document.createElement('optgroup');
            optgroup1.label = world.name;
            
            const optgroup2 = document.createElement('optgroup');
            optgroup2.label = world.name;
            
            world.locations.forEach(location => {
                const option1 = document.createElement('option');
                option1.value = location.id;
                option1.textContent = `${location.name} (${location.dimension})`;
                
                const option2 = document.createElement('option');
                option2.value = location.id;
                option2.textContent = `${location.name} (${location.dimension})`;
                
                optgroup1.appendChild(option1);
                optgroup2.appendChild(option2);
            });
            
            if (world.locations.length > 0) {
                location1Select.appendChild(optgroup1);
                location2Select.appendChild(optgroup2);
            }
        });
    }
    
    // Event Listeners
    locationForm.addEventListener('submit', addLocation);
    filterDimension.addEventListener('change', renderLocations);
    filterCategory.addEventListener('change', renderLocations);
    searchInput.addEventListener('input', renderLocations);
    exportDataBtn.addEventListener('click', exportData);
    importDataInput.addEventListener('change', importData);
    clearDataBtn.addEventListener('click', clearData);
    calculateNetherBtn.addEventListener('click', calculateNetherCoords);
    calculateDistanceBtn.addEventListener('click', calculateDistance);
    
    // World management event listeners
    worldSelector.addEventListener('change', changeWorld);
    createWorldBtn.addEventListener('click', showCreateWorldModal);
    closeModalBtn.addEventListener('click', hideCreateWorldModal);
    createWorldForm.addEventListener('submit', createWorld);
    deleteWorldBtn.addEventListener('click', deleteWorld);
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target == createWorldModal) {
            hideCreateWorldModal();
        }
    });
    
    // Initialize
    updateWorldSelector();
    renderLocations();
    updateLocationSelects();
});