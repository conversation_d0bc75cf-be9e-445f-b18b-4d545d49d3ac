<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-P6DC2C9Q7T"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-P6DC2C9Q7T');
    </script>
    <title>Minecraft Location Tracker - Save and Organize Your Minecraft Coordinates</title>
    <meta name="description"
        content="Free tool to track, organize and manage your Minecraft coordinates across multiple worlds. Save your bases, villages, portals, and other important locations.">
    <meta name="keywords"
        content="Minecraft, coordinates, location tracker, portal calculator, Minecraft worlds, Minecraft bases, Nether portal">
    <meta name="author" content="Minecraft Location Tracker">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="Minecraft Location Tracker - Save and Organize Your Coordinates">
    <meta property="og:description"
        content="Never lose your important Minecraft locations again. Track coordinates across multiple worlds with our free web tool.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://minecraft-location-tracker.com/">

    <link rel="canonical" href="https://minecraft-location-tracker.com/">
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=VT323&display=swap" rel="stylesheet">
</head>

<body>
    <div class="container">
        <header>
            <h1>Minecraft Location Tracker</h1>
            <p class="tagline">Save, organize, and share your important Minecraft coordinates across multiple worlds</p>
            <div class="creator-info">
                <h3>Created by Shushie</h3>
                <div class="creator-links">
                    <a href="https://www.youtube.com/@Shushie_valorant?sub_confirmation=1" class="creator-link" target="_blank" rel="noopener">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23FF0000' d='M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z'/%3E%3C/svg%3E" alt="YouTube">
                        Subscribe on YouTube
                    </a>
                    <a href="https://razorpay.me/@shushie" class="creator-link" target="_blank" rel="noopener">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23795548' d='M20 3H4v10c0 2.21 1.79 4 4 4h6c2.21 0 4-1.79 4-4v-3h2c1.11 0 2-.89 2-2V5c0-1.11-.89-2-2-2zm0 5h-2V5h2v3zM4 19h16v2H4z'/%3E%3C/svg%3E" alt="Coffee">
                        Buy Me a Coffee ☕
                    </a>
                </div>
            </div>
            <nav>
                <a href="how-to-use.html" class="nav-btn">How To Use</a>
            </nav>
        </header>

        <main>
            <section class="world-management">
                <h2>World Management</h2>
                <div class="world-controls">
                    <div class="form-group">
                        <label for="world-selector">Select World:</label>
                        <select id="world-selector" aria-label="Select your Minecraft world">
                            <option value="">Select a world</option>
                            <!-- Worlds will be dynamically added here -->
                        </select>
                    </div>
                    <button id="create-world-btn" class="btn" aria-label="Create a new Minecraft world">Create New
                        World</button>
                    <button id="delete-world-btn" class="btn btn-danger" aria-label="Delete the selected world">Delete
                        World</button>
                </div>
                <div id="create-world-modal" class="modal">
                    <div class="modal-content">
                        <span class="close-modal" aria-label="Close modal">&times;</span>
                        <h3>Create New World</h3>
                        <form id="create-world-form">
                            <div class="form-group">
                                <label for="world-name">World Name:</label>
                                <input type="text" id="world-name" required
                                    aria-label="Enter name for your new Minecraft world">
                            </div>
                            <button type="submit" class="btn">Create</button>
                        </form>
                    </div>
                </div>
                <div class="storage-note">
                    <p><strong>Note:</strong> All data is stored in your browser's local storage. If you clear your
                        browser data, your saved worlds and locations will be lost. Use the Export/Import feature to
                        backup your data or share with friends.</p>
                </div>
            </section>

            <section class="add-location">
                <h2>Add New Location</h2>
                <form id="location-form">
                    <div class="form-group">
                        <label for="location-name">Location Name:</label>
                        <input type="text" id="location-name" required aria-label="Enter a name for this location">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="coord-x">X:</label>
                            <input type="number" id="coord-x" required aria-label="X coordinate">
                        </div>
                        <div class="form-group">
                            <label for="coord-y">Y:</label>
                            <input type="number" id="coord-y" required aria-label="Y coordinate">
                        </div>
                        <div class="form-group">
                            <label for="coord-z">Z:</label>
                            <input type="number" id="coord-z" required aria-label="Z coordinate">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="dimension">Dimension:</label>
                        <select id="dimension" required aria-label="Select dimension">
                            <option value="overworld">Overworld</option>
                            <option value="nether">Nether</option>
                            <option value="end">The End</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="category">Category:</label>
                        <select id="category" required aria-label="Select location category">
                            <option value="home">Home</option>
                            <option value="portal">Portal</option>
                            <option value="village">Village</option>
                            <option value="monument">Monument</option>
                            <option value="stronghold">Stronghold</option>
                            <option value="farm">Farm</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="description">Description:</label>
                        <textarea id="description" rows="3"
                            aria-label="Enter a description for this location"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="color">Color Tag:</label>
                        <input type="color" id="color" value="#4CAF50" aria-label="Choose a color for this location">
                    </div>

                    <button type="submit" class="btn">Save Location</button>
                </form>
            </section>

            <section class="tools">
                <h2>Tools</h2>
                <div class="tool-container">
                    <div class="tool">
                        <h3>Nether Portal Calculator</h3>
                        <p class="tool-description">Calculate the corresponding coordinates between Overworld and Nether
                            (1:8 ratio).</p>
                        <div class="calculator-container">
                            <div class="calculator-section">
                                <h4>Overworld Coordinates</h4>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="overworld-x">X:</label>
                                        <input type="number" id="overworld-x" aria-label="Overworld X coordinate">
                                    </div>
                                    <div class="form-group">
                                        <label for="overworld-z">Z:</label>
                                        <input type="number" id="overworld-z" aria-label="Overworld Z coordinate">
                                    </div>
                                </div>
                            </div>

                            <div class="calculator-divider">
                                <span>⟷</span>
                            </div>

                            <div class="calculator-section">
                                <h4>Nether Coordinates</h4>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="nether-x">X:</label>
                                        <input type="number" id="nether-x" aria-label="Nether X coordinate">
                                    </div>
                                    <div class="form-group">
                                        <label for="nether-z">Z:</label>
                                        <input type="number" id="nether-z" aria-label="Nether Z coordinate">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p class="calculator-help">Enter coordinates in either Overworld or Nether fields, then click
                            Calculate to find the corresponding coordinates.</p>
                        <button id="calculate-nether" class="btn">Calculate</button>
                        <div id="nether-result" class="result" aria-live="polite"></div>
                    </div>

                    <div class="tool">
                        <h3>Distance Calculator</h3>
                        <p class="tool-description">Calculate the straight-line distance between any two saved
                            locations.</p>
                        <div class="form-group">
                            <label for="location1">Location 1:</label>
                            <select id="location1" aria-label="Select first location">
                                <option value="">Select a location</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="location2">Location 2:</label>
                            <select id="location2" aria-label="Select second location">
                                <option value="">Select a location</option>
                            </select>
                        </div>
                        <button id="calculate-distance" class="btn">Calculate Distance</button>
                        <div id="distance-result" class="result" aria-live="polite"></div>
                    </div>
                </div>
            </section>

            <section class="locations">
                <div class="locations-header">
                    <h2>Saved Locations</h2>
                    <div class="filter-controls">
                        <div class="form-group">
                            <label for="filter-dimension">Filter by Dimension:</label>
                            <select id="filter-dimension" aria-label="Filter locations by dimension">
                                <option value="all">All Dimensions</option>
                                <option value="overworld">Overworld</option>
                                <option value="nether">Nether</option>
                                <option value="end">The End</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="filter-category">Filter by Category:</label>
                            <select id="filter-category" aria-label="Filter locations by category">
                                <option value="all">All Categories</option>
                                <option value="home">Home</option>
                                <option value="portal">Portal</option>
                                <option value="village">Village</option>
                                <option value="monument">Monument</option>
                                <option value="stronghold">Stronghold</option>
                                <option value="farm">Farm</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="search">Search:</label>
                            <input type="text" id="search" placeholder="Search locations..."
                                aria-label="Search locations by name or description">
                        </div>
                    </div>
                </div>

                <div id="locations-container" class="locations-grid" aria-live="polite">
                    <!-- Locations will be dynamically added here -->
                </div>

                <div class="data-controls">
                    <button id="export-data" class="btn" aria-label="Export all worlds and locations to a file">Export
                        Data</button>
                    <div class="import-container">
                        <label for="import-data" class="btn" aria-label="Import worlds and locations from a file">Import
                            Data</label>
                        <input type="file" id="import-data" accept=".json">
                    </div>
                    <button id="clear-data" class="btn btn-danger"
                        aria-label="Clear all worlds and locations data">Clear All Data</button>
                </div>
            </section>
        </main>

        <footer>
            <p>Minecraft Location Tracker &copy; 2025 - A tool for Minecraft players</p>
            <p class="seo-description">This free web tool helps Minecraft players track coordinates of important
                locations like bases, villages, portals, and more across multiple worlds. Save and organize your
                Minecraft coordinates easily!</p>
        </footer>
    </div>

    <!-- Templates -->
    <template id="location-card-template">
        <div class="location-card">
            <div class="location-header">
                <h3 class="location-name"></h3>
                <div class="location-category"></div>
            </div>
            <div class="location-coords">
                <span class="coord-x"></span>,
                <span class="coord-y"></span>,
                <span class="coord-z"></span>
            </div>
            <div class="location-dimension"></div>
            <div class="location-description"></div>
            <div class="location-actions">
                <button class="btn-copy" aria-label="Copy coordinates">Copy Coords</button>
                <button class="btn-edit" aria-label="Edit location">Edit</button>
                <button class="btn-delete" aria-label="Delete location">Delete</button>
            </div>
        </div>
    </template>

    <script src="script.js"></script>
</body>

</html>
