:root {
    --dirt-brown: #8B5A2B;
    --grass-green: #4CAF50;
    --stone-gray: #808080;
    --obsidian-purple: #2D0C3F;
    --water-blue: #3498db;
    --lava-orange: #e67e22;
    --netherrack-red: #993333;
    --end-stone: #FFFACD;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'VT323', monospace;
    background-color: #f0f0f0;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAIAAACRXR/mAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAABnSURBVHja7M5BDQAwDASh+je9/aXhCQSk5eUzM/3AsCqGVTGsimFVDKtiWBXDqhhWxbAqhlUxrIphVQyrYlgVw6oYVsWwKoZVMayKYVUMq2JYFcOqGFbFsCqGVTGsimFVDKvyPAUYAHlsUkHVNy2SAAAAAElFTkSuQmCC');
    background-repeat: repeat;
    color: #333;
    line-height: 1.6;
    font-size: 18px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    background-color: var(--dirt-brown);
    padding: 20px;
    border: 4px solid #5D4037;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

header nav {
    margin-top: 15px;
}

.nav-btn {
    display: inline-block;
    background-color: #5D4037;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border: 2px solid #3E2723;
    transition: all 0.3s;
    font-size: 18px;
}

.nav-btn:hover {
    background-color: #3E2723;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

h1 {
    font-size: 3rem;
    color: #fff;
    text-shadow: 2px 2px 0 #000, -2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000;
    letter-spacing: 2px;
}

h2 {
    font-size: 2rem;
    margin-bottom: 20px;
    color: var(--dirt-brown);
    text-shadow: 1px 1px 0 #fff;
    border-bottom: 3px solid var(--dirt-brown);
    padding-bottom: 5px;
}

h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--dirt-brown);
}

section {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 20px;
    margin-bottom: 30px;
    border: 4px solid #5D4037;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.world-management {
    position: relative;
}

.world-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.world-controls .form-group {
    flex: 1;
    min-width: 200px;
    margin-bottom: 0;
}

.world-controls button {
    height: 42px; /* Match the height of the select dropdown */
    margin-top: 32px;
}

.storage-note {
    background-color: #FFF9C4;
    border: 2px solid #FBC02D;
    padding: 15px;
    margin-top: 20px;
    border-radius: 5px;
    font-size: 0.9rem;
}

.modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.modal-content {
    background-color: rgba(255, 255, 255, 0.95);
    margin: 15% auto;
    padding: 20px;
    border: 4px solid var(--dirt-brown);
    width: 80%;
    max-width: 500px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
}

.close-modal {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 28px;
    cursor: pointer;
}

.form-group {
    margin-bottom: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--dirt-brown);
}

input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid var(--dirt-brown);
    font-family: 'VT323', monospace;
    font-size: 18px;
    background-color: #f9f9f9;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--grass-green);
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
}

.btn {
    background-color: var(--grass-green);
    color: white;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    font-family: 'VT323', monospace;
    font-size: 18px;
    border: 2px solid #2E7D32;
    transition: all 0.3s;
}

.btn:hover {
    background-color: #388E3C;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-danger {
    background-color: #f44336;
    border-color: #d32f2f;
}

.btn-danger:hover {
    background-color: #d32f2f;
}

.locations-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.filter-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-controls .form-group {
    min-width: 200px;
}

.locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.location-card {
    background-color: #fff;
    border: 3px solid var(--dirt-brown);
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    position: relative;
}

.location-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.location-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.location-category {
    font-size: 0.9rem;
    padding: 3px 8px;
    border-radius: 3px;
    background-color: var(--grass-green);
    color: white;
}

.location-coords {
    font-weight: bold;
    margin-bottom: 5px;
}

.location-dimension {
    margin-bottom: 10px;
    font-style: italic;
}

.location-description {
    margin-bottom: 15px;
    border-top: 1px solid #ddd;
    padding-top: 10px;
}

.location-actions {
    display: flex;
    gap: 10px;
}

.location-actions button {
    padding: 5px 10px;
    font-size: 0.9rem;
}

.btn-copy {
    background-color: var(--water-blue);
    color: white;
    border: none;
    cursor: pointer;
    border: 1px solid #2980b9;
}

.btn-edit {
    background-color: var(--lava-orange);
    color: white;
    border: none;
    cursor: pointer;
    border: 1px solid #d35400;
}

.btn-delete {
    background-color: #f44336;
    color: white;
    border: none;
    cursor: pointer;
    border: 1px solid #d32f2f;
}

.btn-copy:hover, .btn-edit:hover, .btn-delete:hover {
    filter: brightness(90%);
}

.data-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.import-container {
    position: relative;
    overflow: hidden;
}

.import-container label {
    display: inline-block;
    cursor: pointer;
}

.import-container input[type="file"] {
    position: absolute;
    opacity: 0;
    left: 0;
    top: 0;
    width: 0;
    height: 0;
}

.tool-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.tool {
    background-color: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border: 2px solid var(--dirt-brown);
    border-radius: 5px;
}

.result {
    margin-top: 15px;
    padding: 10px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* Dimension-specific styles */
.dimension-overworld {
    border-left: 5px solid var(--grass-green);
}

.dimension-nether {
    border-left: 5px solid var(--netherrack-red);
}

.dimension-end {
    border-left: 5px solid var(--end-stone);
}

/* Category-specific colors */
.category-home { background-color: var(--grass-green); }
.category-portal { background-color: var(--obsidian-purple); }
.category-village { background-color: var(--water-blue); }
.category-monument { background-color: var(--stone-gray); }
.category-stronghold { background-color: var(--end-stone); color: #333; }
.category-farm { background-color: #FFC107; }
.category-other { background-color: #9C27B0; }

footer {
    text-align: center;
    padding: 15px;
    background-color: var(--dirt-brown);
    color: white;
    margin-top: 30px;
    border: 4px solid #5D4037;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .locations-header {
        flex-direction: column;
    }
    
    .filter-controls {
        margin-top: 15px;
        width: 100%;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .locations-grid {
        grid-template-columns: 1fr;
    }
    
    .tool-container {
        grid-template-columns: 1fr;
    }
    
    .data-controls {
        flex-direction: column;
    }
    
    .data-controls button, .import-container {
        width: 100%;
    }
}

.no-world-selected {
    text-align: center;
    padding: 30px;
    background-color: #f5f5f5;
    border: 2px dashed #ccc;
    margin: 20px 0;
    color: #666;
}

.worlds-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.world-card {
    background-color: #fff;
    border: 3px solid var(--dirt-brown);
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.world-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    background-color: #f9f9f9;
}

.world-card.active {
    border-color: var(--grass-green);
    background-color: #E8F5E9;
}

.world-card h3 {
    margin-bottom: 10px;
    text-align: center;
}

.world-stats {
    font-size: 0.9rem;
    color: #666;
    text-align: center;
}

.empty-state {
    text-align: center;
    padding: 30px;
    color: #888;
}

.empty-state p {
    margin-bottom: 15px;
}

/* Add styles for the tagline */
.tagline {
    color: #fff;
    font-size: 1.2rem;
    margin-top: 10px;
    opacity: 0.9;
}

/* Calculator styles */
.tool-description {
    margin-bottom: 15px;
    color: #555;
}

.calculator-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.calculator-section {
    flex: 1;
    min-width: 200px;
    background-color: rgba(255, 255, 255, 0.5);
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.calculator-section h4 {
    margin-bottom: 10px;
    color: var(--dirt-brown);
    text-align: center;
    font-size: 1.1rem;
}

.calculator-divider {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    color: var(--dirt-brown);
    padding: 0 10px;
}

.calculator-help {
    font-size: 0.9rem;
    font-style: italic;
    color: #666;
    margin-bottom: 15px;
}

/* SEO Footer */
.seo-description {
    font-size: 0.85rem;
    opacity: 0.8;
    margin-top: 10px;
    max-width: 80%;
    margin: 10px auto 0;
}

/* Creator Info Section */
.creator-info {
    margin: 15px 0;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border: 2px solid #FFC107;
    text-align: center;
}

.creator-info h3 {
    color: #FFC107;
    font-size: 1.3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.5);
}

.creator-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.creator-link {
    display: inline-flex;
    align-items: center;
    background-color: #FFC107;
    color: #000;
    padding: 8px 16px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    border: 2px solid #FFA000;
    font-size: 0.9rem;
}

.creator-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    background-color: #FFD54F;
}

.creator-link img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
}

@media (max-width: 768px) {
    .creator-links {
        gap: 10px;
    }
    
    .creator-link {
        padding: 6px 12px;
        font-size: 0.85rem;
    }
    
    .creator-link img {
        width: 16px;
        height: 16px;
        margin-right: 6px;
    }
}

/* Accessibility improvements */
input:focus, select:focus, textarea:focus, button:focus {
    outline: 2px solid var(--grass-green);
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
}

.no-results, .empty-state {
    text-align: center;
    padding: 30px;
    background-color: #f5f5f5;
    border: 2px dashed #ccc;
    margin: 20px 0;
    color: #666;
    border-radius: 5px;
}